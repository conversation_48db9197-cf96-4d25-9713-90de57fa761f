<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:tblPr>
    <w:tblStyle w:val="TableGrid"/>
    <w:tblW w:w="${styling.table.width!''}" w:type="${styling.table.widthType!''}"/>
    <w:tblBorders>
      <#-- Only render borders if not 'nil' -->
      <#if styling.table.borders.top?? && styling.table.borders.top != "nil">
        <w:top w:val="${styling.table.borders.top!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.left?? && styling.table.borders.left != "nil">
        <w:left w:val="${styling.table.borders.left!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.bottom?? && styling.table.borders.bottom != "nil">
        <w:bottom w:val="${styling.table.borders.bottom!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.right?? && styling.table.borders.right != "nil">
        <w:right w:val="${styling.table.borders.right!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.insideH?? && styling.table.borders.insideH != "nil">
        <w:insideH w:val="${styling.table.borders.insideH!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.insideV?? && styling.table.borders.insideV != "nil">
        <w:insideV w:val="${styling.table.borders.insideV!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
    </w:tblBorders>
  </w:tblPr>
  <w:tblGrid>
    <#-- Check if any section has className to determine if we need the class column -->
    <#assign hasClassName = false>
    <#if sections??>
      <#list sections as section>
        <#if section.className??>
          <#assign hasClassName = true>
          <#break>
        </#if>
      </#list>
    </#if>

    <#if hasClassName>
      <w:gridCol w:w="1800"/>
    </#if>
    <w:gridCol w:w="${(styling.rows.custom.cellMaxWidth)!'5000'}"/>
    <w:gridCol w:w="${(styling.rows.custom.cellMinWidth)!'2000'}"/>
    <#list carriers as carrier>
      <w:gridCol w:w="${(styling.rows.custom.cellMinWidth)!'2000'}"/>
      <w:gridCol w:w="${(styling.rows.custom.cellMinWidth)!'2000'}"/>
    </#list>
  </w:tblGrid>

  <!-- Header row -->
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${styling.rows.header.height!''}"/>
    </w:trPr>

    <!-- Class header column (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="1800" w:type="dxa"/>
          <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor!''}"/>
          <w:vAlign w:val="${styling.rows.header.cell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.header.text.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.header.text.color!''}"/>
              <w:sz w:val="${styling.rows.header.text.fontSize!''}"/>
              <#if styling.rows.header.text.bold!false><w:b/></#if>
            </w:rPr>
            <w:t>Class</w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMaxWidth)!'5000'}" w:type="dxa"/>
        <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor!''}"/>
        <w:vAlign w:val="${styling.rows.header.cell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.header.text.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.header.text.color!''}"/>
            <w:sz w:val="${styling.rows.header.text.fontSize!''}"/>
            <#if styling.rows.header.text.bold!false><w:b/></#if>
          </w:rPr>
          <w:t>Benefit</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Volume header column -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
        <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor!''}"/>
        <w:vAlign w:val="${styling.rows.header.cell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.header.text.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.header.text.color!''}"/>
            <w:sz w:val="${styling.rows.header.text.fontSize!''}"/>
            <#if styling.rows.header.text.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if carriers??>
      <#list carriers as carrier>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor!''}"/>
            <w:vAlign w:val="${styling.rows.header.cell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.header.text.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.header.text.color!''}"/>
                <w:sz w:val="${styling.rows.header.text.fontSize!''}"/>
                <#if styling.rows.header.text.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${carrier}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>

  <!-- Subheader row -->
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${styling.rows.subheader.height!''}"/>
    </w:trPr>

    <!-- Empty class subheader (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="1800" w:type="dxa"/>
          <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.subheader.cell.backgroundColor!''}"/>
          <w:vAlign w:val="${styling.rows.subheader.cell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.subheader.text.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.subheader.text.color!''}"/>
              <w:sz w:val="${styling.rows.subheader.text.fontSize!''}"/>
              <#if styling.rows.subheader.text.bold!false><w:b/></#if>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMaxWidth)!'5000'}" w:type="dxa"/>
        <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.subheader.cell.backgroundColor!''}"/>
        <w:vAlign w:val="${styling.rows.subheader.cell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.subheader.text.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.subheader.text.color!''}"/>
            <w:sz w:val="${styling.rows.subheader.text.fontSize!''}"/>
            <#if styling.rows.subheader.text.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume subheader -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
        <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.subheader.cell.backgroundColor!''}"/>
        <w:vAlign w:val="${styling.rows.subheader.cell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.subheader.text.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.subheader.text.color!''}"/>
            <w:sz w:val="${styling.rows.subheader.text.fontSize!''}"/>
            <#if styling.rows.subheader.text.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if carriers??>
      <#list carriers as carrier>
        <#list ["Rates", "Premium"] as label>
          <w:tc>
            <w:tcPr>
              <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
              <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.subheader.cell.backgroundColor!''}"/>
              <w:vAlign w:val="${styling.rows.subheader.cell.verticalAlign!''}"/>
            </w:tcPr>
            <w:p>
              <w:pPr>
                <w:jc w:val="${styling.rows.subheader.text.alignment!''}"/>
              </w:pPr>
              <w:r>
                <w:rPr>
                  <w:color w:val="${styling.rows.subheader.text.color!''}"/>
                  <w:sz w:val="${styling.rows.subheader.text.fontSize!''}"/>
                  <#if styling.rows.subheader.text.bold!false><w:b/></#if>
                </w:rPr>
                <w:t>${label}</w:t>
              </w:r>
            </w:p>
          </w:tc>
        </#list>
      </#list>
    </#if>
  </w:tr>

  <!-- Benefit rows -->
  <#if sections??>
    <#list sections as section>
      <w:tr>
        <w:trPr>
          <w:trHeight w:val="${styling.rows.benefit.height!''}"/>
        </w:trPr>

        <!-- Class name cell (conditional) -->
        <#if hasClassName>
          <w:tc>
            <w:tcPr>
              <w:tcW w:w="1800" w:type="dxa"/>
              <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
            </w:tcPr>
            <w:p>
              <w:pPr>
                <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
              </w:pPr>
              <w:r>
                <w:rPr>
                  <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
                  <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
                  <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
                </w:rPr>
                <w:t>${section.className!''}</w:t>
              </w:r>
            </w:p>
          </w:tc>
        </#if>

        <w:tc>
          <w:tcPr>
            <w:tcW w:w="${(styling.rows.custom.cellMaxWidth)!'5000'}" w:type="dxa"/>
            <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
                <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
                <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${section.name}</w:t>
            </w:r>
          </w:p>
        </w:tc>

        <!-- Volume column -->
        <w:tc>
          <w:tcPr>
            <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize!''}"/>
                <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>
                <#if carriers??>
                  <#list carriers as carrier>
                    <#assign val = section.values[carrier]!{}>
                    <#if val.volume?? && val.volume?has_content && val.volume?is_number && val.volume != 0>
                      ${val.volume}
                      <#break>
                    </#if>
                  </#list>
                </#if>
              </w:t>
            </w:r>
          </w:p>
        </w:tc>

        <#if carriers??>
          <#list carriers as carrier>
            <#assign val = section.values[carrier]!{}>
            <#list ["rate", "premium"] as key>
              <w:tc>
                <w:tcPr>
                  <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
                  <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
                </w:tcPr>
                <w:p>
                  <w:pPr>
                    <w:jc w:val="right"/>
                  </w:pPr>
                  <w:r>
                    <w:rPr>
                      <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                      <w:sz w:val="${styling.rows.benefit.carrierText.fontSize!''}"/>
                      <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
                    </w:rPr>
                    <w:t>${val[key]!''}</w:t>
                  </w:r>
                </w:p>
              </w:tc>
            </#list>
          </#list>
        </#if>
      </w:tr>
    </#list>
  </#if>
    <!-- Empty spacing row -->
  <w:tr>
    <!-- Class column spacing (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="1800" w:type="dxa"/>
          <w:vAlign w:val="center"/>
        </w:tcPr>
        <w:p>
          <w:pPr><w:spacing w:after="120"/></w:pPr>
          <w:r><w:t></w:t></w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMaxWidth)!'5000'}" w:type="dxa"/>
        <w:vAlign w:val="center"/>
      </w:tcPr>
      <w:p>
        <w:pPr><w:spacing w:after="120"/></w:pPr>
        <w:r><w:t></w:t></w:r>
      </w:p>
    </w:tc>

    <!-- Volume column spacing -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
        <w:vAlign w:val="center"/>
      </w:tcPr>
      <w:p>
        <w:pPr><w:spacing w:after="120"/></w:pPr>
        <w:r><w:t></w:t></w:r>
      </w:p>
    </w:tc>
    
    <#if carriers??>
      <#list carriers as carrier>
        <#list 1..2 as x>
          <w:tc>
            <w:tcPr>
              <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
              <w:vAlign w:val="center"/>
            </w:tcPr>
            <w:p>
              <w:pPr><w:spacing w:after="120"/></w:pPr>
              <w:r><w:t></w:t></w:r>
            </w:p>
          </w:tc>
        </#list>
      </#list>
    </#if>
  </w:tr>

  <!-- Total Monthly Premiums row (conditional) -->
  <#if calculations?? && calculations?has_content>
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${styling.rows.benefit.height!''}"/>
    </w:trPr>

    <!-- Empty class cell for totals (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="1800" w:type="dxa"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:b/>
              <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMaxWidth)!'5000'}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
            <w:b/>
            <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
          </w:rPr>
          <w:t>Total Monthly Premiums</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume cell for totals -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
            <w:b/>
            <w:sz w:val="${styling.rows.benefit.carrierText.fontSize!''}"/>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if calculations??>
      <#list calculations as calc>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:b/>
                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize!''}"/>
              </w:rPr>
              <w:t>${calc.totalMonthlyPremiums!''}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>
  </#if>

  <!-- Annual Premium row (conditional) -->
  <#if calculations?? && calculations?has_content>
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${styling.rows.benefit.height!''}"/>
    </w:trPr>

    <!-- Empty class cell (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="1800" w:type="dxa"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
              <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMaxWidth)!'5000'}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
            <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t>Annual Premium</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume cell -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
            <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if calculations??>
      <#list calculations as calc>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize!''}"/>
                <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${calc.annualPremium!''}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>
  </#if>

  <!-- $ Difference From #1 row (conditional) -->
  <#if calculations?? && calculations?has_content && calculations?filter(calc -> calc["$ Difference From #1"]??)?has_content>
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${styling.rows.benefit.height!''}"/>
    </w:trPr>

    <!-- Empty class cell (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="1800" w:type="dxa"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
              <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMaxWidth)!'5000'}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
            <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t>$ Difference From #1</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume cell -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
            <w:sz w:val="${styling.rows.benefit.carrierText.fontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if calculations??>
      <#list calculations as calc>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize!''}"/>
                <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${calc["$ Difference From #1"]!''}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>
  </#if>

  <!-- Percentage Different From #1 row (conditional) -->
  <#if calculations?? && calculations?has_content && calculations?filter(calc -> calc["Percentage Different From #1"]??)?has_content>
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${styling.rows.benefit.height!''}"/>
    </w:trPr>

    <!-- Empty class cell (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="1800" w:type="dxa"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
              <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMaxWidth)!'5000'}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
            <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t>Percentage Different From #1</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume cell -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${(styling.rows.custom.cellMinWidth)!'2000'}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
            <w:sz w:val="${styling.rows.benefit.carrierText.fontSize!''}"/>
            <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if calculations??>
      <#list calculations as calc>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize!''}"/>
                <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${calc["Percentage Different From #1"]!''}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>
  </#if>

  <!-- Class Legend row (conditional) -->
  <#if classLegend?? && classLegend?has_content>
    <w:tr>
      <w:trPr>
        <w:trHeight w:val="${styling.rows.benefit.height!''}"/>
      </w:trPr>
      <w:tc>
        <w:tcPr>
          <w:gridSpan w:val="${(carriers?size * 2 + 2 + (hasClassName?then(1, 0)))!''}"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
            </w:rPr>
            <w:t>Class Legend: </w:t>
          </w:r>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${styling.rows.benefit.benefitText.fontSize!''}"/>
            </w:rPr>
            <w:t><#if classLegend?is_sequence>${classLegend?join(", ")}<#else>${classLegend}</#if></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </w:tr>
  </#if>
</w:tbl>