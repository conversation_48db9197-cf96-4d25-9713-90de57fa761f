-- Multi-class Rate Sheet Function v2 with Pagination Support
-- This function supports both single-class (RTQ) and multi-class scenarios
-- Focuses on sections structure with exact class names and implements pagination with 16 sections per page

CREATE OR REPLACE FUNCTION sandf.fn_get_rate_sheet_v2(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_plan_id INTEGER;
    quote_record RECORD;
    quote_json JSONB;
    benefit_premiums JSONB;
    carrier_name TEXT;
    carrier_order INTEGER;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array TEXT[];

    -- Section data structures
    sections_map JSONB := '{}'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefit_values JSONB;
    section_key TEXT;

    -- Pagination variables
    MAX_SECTIONS_PER_PAGE INTEGER := 16;
    total_sections INTEGER;
    current_page_sections INTEGER := 0;
    current_page_sections_array JSONB := '[]'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    section_idx INTEGER;

    -- Multi-class support variables
    employee_classes TEXT[];
    employee_class_count INTEGER;
    current_employee_class TEXT;
    class_suffix TEXT;
    class_number TEXT;

    -- Class grouping and legend variables
    class_to_letter_map JSONB := '{}'::jsonb;
    class_legend_array TEXT[] := ARRAY[]::TEXT[];
    letter_index INTEGER := 1;
    current_letter TEXT;

    -- Benefit processing variables
    key_name TEXT;
    val JSONB;
    subval JSONB;
    subkey_name TEXT;
    mapped_key_name TEXT;
    friendly_name TEXT;
    volume_text TEXT;
    volume_numeric NUMERIC;
    coverage_order TEXT[] := ARRAY['single', 'couple', 'family'];
    coverage_type TEXT;
    should_include_key BOOLEAN;

    -- Configuration from config table
    config_json JSONB;
    benefit_to_premium_map JSONB;
    friendly_name_map JSONB;

    -- Calculations variables
    calculations_array JSONB := '[]'::jsonb;
    calculation_obj JSONB;
    carrier_total_monthly NUMERIC;
    carrier_total_annual NUMERIC;
    lowest_monthly NUMERIC := NULL;
    lowest_annual NUMERIC := NULL;
    percentage_diff NUMERIC;
    calculation_idx INTEGER;

    -- Premium calculation variables
    monthly_premiums_map JSONB := '{}'::jsonb;
    annual_premiums_map JSONB := '{}'::jsonb;
    first_annual_premium NUMERIC;
    total_monthly_premium NUMERIC;
    total_annual_premium NUMERIC;
    premium_value NUMERIC;
    calc_obj JSONB;
    
BEGIN
    -- Get configuration from config table
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    -- Extract benefitToPremium mapping
    IF config_json IS NOT NULL THEN
        benefit_to_premium_map := config_json -> 'benefitToPremium';
        friendly_name_map := config_json -> 'friendlyNameMap';
    ELSE
        benefit_to_premium_map := '{}'::jsonb;
        friendly_name_map := '{}'::jsonb;
    END IF;

    -- Get plan_id from plan_uuid
    SELECT plan_id INTO v_plan_id
    FROM sandf.plan
    WHERE plan_uuid = plan_uuid_param::uuid;

    IF v_plan_id IS NULL THEN
        RETURN jsonb_build_array(
            jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            )
        );
    END IF;

    -- Step 1: Detect employee classes for this plan
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    RAISE NOTICE 'Found % employee classes: %', employee_class_count, employee_classes;

    -- Step 1.5: Build class to letter mapping (A, B, C, etc.)
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        current_letter := chr(64 + letter_index); -- A=65, B=66, etc.
        class_to_letter_map := class_to_letter_map || jsonb_build_object(current_employee_class, current_letter);
        class_legend_array := array_append(class_legend_array, current_letter || ' - ' || current_employee_class);
        letter_index := letter_index + 1;
    END LOOP;

    RAISE NOTICE 'Class to letter mapping: %', class_to_letter_map;
    RAISE NOTICE 'Class legend: %', class_legend_array;

    -- Step 2: Build carrier order map for all employee classes
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid,
               ec.name as employee_class_name
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = ANY(employee_classes)
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
    LOOP
        carrier_name := quote_record.carrier_description;

        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999
        );

        carrier_order_map := carrier_order_map || jsonb_build_object(
            carrier_name,
            jsonb_build_object('order', carrier_order)
        );
    END LOOP;

    -- Step 3: Build ordered carriers array
    SELECT array_agg(carriers.carrier_name ORDER BY (carrier_order_map -> carriers.carrier_name ->> 'order')::integer ASC)
    INTO ordered_carriers_array
    FROM (
        SELECT DISTINCT jsonb_object_keys(carrier_order_map) as carrier_name
    ) carriers;
    
    -- Step 4: Build sections with class grouping and premium matching
    -- First collect all section data by class, then group and combine matching premiums
    DECLARE
        class_sections_map JSONB := '{}'::jsonb;  -- Store sections by class
        combined_sections_map JSONB := '{}'::jsonb;  -- Store final combined sections
        section_signature TEXT;  -- For matching premiums across classes
        matching_classes TEXT[];  -- Classes with matching premiums
        combined_class_name TEXT;  -- Final className for section
    BEGIN

    -- Process each employee class and collect section data
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        class_suffix := current_employee_class;
        current_letter := class_to_letter_map ->> current_employee_class;

        -- Extract class number from employee class name
        -- Examples: " Class 1 - Salaried EE's" -> "1", " Class 2 - Hourly EE's" -> "2"
        class_number := CASE
            WHEN current_employee_class ~ 'Class [0-9]+' THEN
                regexp_replace(current_employee_class, '.*Class ([0-9]+).*', '\1')
            ELSE
                '1'  -- Default to 1 if no class number found
        END;

        RAISE NOTICE 'Processing employee class: % (class number: %, letter: %)', current_employee_class, class_number, current_letter;

        -- Initialize sections map for this class if not exists
        IF NOT class_sections_map ? current_employee_class THEN
            class_sections_map := class_sections_map || jsonb_build_object(current_employee_class, '{}'::jsonb);
        END IF;

        -- Process all quotes for this employee class to populate section values
        FOR quote_record IN
            SELECT ecq.formatted_quote_details,
                   c.description as carrier_description,
                   q.quote_id,
                   q.quote_uuid
            FROM sandf.plan p
            JOIN sandf.quote q ON q.plan_id = p.plan_id
            JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
            JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
            JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
            WHERE p.plan_uuid = plan_uuid_param::uuid
            AND ec.name = current_employee_class
            AND ecq.formatted_quote_details IS NOT NULL
            AND ecq.formatted_quote_details != '{}'::jsonb
            ORDER BY
                COALESCE((carrier_order_map -> c.description ->> 'order')::integer, 999999) ASC,
                c.description ASC
        LOOP
            quote_json := quote_record.formatted_quote_details::jsonb;
            benefit_premiums := quote_json -> 'benefitPremiums';
            carrier_name := quote_record.carrier_description;

            RAISE NOTICE 'Processing carrier: % for class: %', carrier_name, current_employee_class;

            -- Process each benefit premium (following original rate-sheet.sql pattern)
            FOR key_name, val IN
                SELECT j.k1, j.v1
                FROM jsonb_each(benefit_premiums) AS j(k1, v1)
            LOOP
                -- Skip the pre-calculated total fields
                IF key_name NOT IN ('totalMonthlyPremiums', 'annualPremium') THEN
                    -- Get mapped key name from config
                    IF benefit_to_premium_map ? key_name THEN
                        mapped_key_name := benefit_to_premium_map ->> key_name;
                    ELSE
                        mapped_key_name := key_name;
                    END IF;

                    -- Check if key should be included based on includes/excludes parameters
                    should_include_key := FALSE;

                    IF array_length(includes_param, 1) IS NULL THEN
                        -- No includes filter, check excludes only
                        IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                            should_include_key := TRUE;
                        END IF;
                    ELSE
                        -- Includes filter exists, key must be in includes and not in excludes
                        IF mapped_key_name = ANY(includes_param) THEN
                            IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                                should_include_key := TRUE;
                            END IF;
                        END IF;
                    END IF;

                    -- Only process if key should be included
                    IF should_include_key THEN
                        -- Get friendly name from ui_field table using friendly field instead of name
                        SELECT uf.friendly INTO friendly_name
                        FROM sandf.ui_field uf
                        WHERE uf.name = key_name
                        LIMIT 1;

                        -- Fallback to config friendlyNameMap if not found in ui_field
                        IF friendly_name IS NULL THEN
                            IF friendly_name_map ? key_name THEN
                                friendly_name := friendly_name_map ->> key_name;
                            ELSE
                                friendly_name := initcap(replace(key_name, '_', ' '));
                            END IF;
                        END IF;

                        -- Check if this is a nested structure (like extendedHealth with single/couple/family)
                        IF val ? 'single' OR val ? 'couple' OR val ? 'family' THEN
                            -- Handle nested premium structure (following original pattern)
                            FOREACH coverage_type IN ARRAY coverage_order
                            LOOP
                                IF val ? coverage_type THEN
                                    subval := val -> coverage_type;
                                    subkey_name := coverage_type;

                                    -- Create section key without class number for grouping (use original key_name, not mapped)
                                    section_key := key_name || '_' || subkey_name;

                                    -- Look for existing section in this class's sections
                                    section_obj := class_sections_map -> current_employee_class -> section_key;
                                    IF section_obj IS NOT NULL THEN
                                        benefit_values := section_obj->'values';
                                    ELSE
                                        benefit_values := '{}'::jsonb;
                                    END IF;

                                -- Convert volume to numeric, default to 0 if conversion fails
                                BEGIN
                                    volume_text := COALESCE(subval->>'volume', '0');
                                    volume_numeric := sandf.safe_parse_numeric(volume_text);
                                EXCEPTION WHEN OTHERS THEN
                                    volume_numeric := 0;
                                END;

                                -- Handle string/numeric premium values properly for display
                                DECLARE
                                    display_rate TEXT;
                                    display_premium TEXT;
                                    raw_premium_val TEXT;
                                    raw_rate_val TEXT;
                                BEGIN
                                    -- Get raw values
                                    raw_premium_val := COALESCE(subval->>'premium', '-');
                                    raw_rate_val := COALESCE(subval->>'rate', '-');

                                    RAISE NOTICE 'Raw values - Rate: %, Premium: %', raw_rate_val, raw_premium_val;

                                    -- Format for display (handle both string and numeric)
                                    IF raw_premium_val = '-' THEN
                                        display_premium := '-';
                                    ELSE
                                        -- If it's already formatted with $, keep it; otherwise add $
                                        IF raw_premium_val LIKE '$%' THEN
                                            display_premium := raw_premium_val;
                                        ELSE
                                            display_premium := '$' || raw_premium_val;
                                        END IF;
                                    END IF;

                                    IF raw_rate_val = '-' THEN
                                        display_rate := '-';
                                    ELSE
                                        -- If it's already formatted with $, keep it; otherwise add $
                                        IF raw_rate_val LIKE '$%' THEN
                                            display_rate := raw_rate_val;
                                        ELSE
                                            display_rate := '$' || raw_rate_val;
                                        END IF;
                                    END IF;

                                    RAISE NOTICE 'Display values - Rate: %, Premium: %', display_rate, display_premium;

                                    benefit_values := benefit_values || jsonb_build_object(
                                        carrier_name,
                                        jsonb_build_object(
                                            'rate', to_jsonb(display_rate),
                                            'volume', to_jsonb(volume_numeric),
                                            'premium', to_jsonb(display_premium)
                                        )
                                    );
                                END;

                                    -- Build section object (store in class sections map)
                                    section_obj := jsonb_build_object(
                                        'id', section_key,
                                        'name', friendly_name || ' ' || initcap(subkey_name),
                                        'values', benefit_values,
                                        'sort_key', key_name || '_' || subkey_name,
                                        'coverage_order', array_position(coverage_order, coverage_type),
                                        'base_benefit', key_name,
                                        'coverage_type', subkey_name,
                                        'class_number', class_number::integer,
                                        'employee_class', current_employee_class
                                    );

                                    -- Store section in class sections map
                                    class_sections_map := jsonb_set(
                                        class_sections_map,
                                        ARRAY[current_employee_class, section_key],
                                        section_obj
                                    );
                            END IF;
                        END LOOP;

                        ELSE
                            -- Handle direct premium values (following original pattern)
                            -- Create section key without class number for grouping (use original key_name, not mapped)
                            section_key := key_name;

                            -- Look for existing section in this class's sections
                            section_obj := class_sections_map -> current_employee_class -> section_key;
                            IF section_obj IS NOT NULL THEN
                                benefit_values := section_obj->'values';
                            ELSE
                                benefit_values := '{}'::jsonb;
                            END IF;

                        -- Convert volume to numeric, default to 0 if conversion fails
                        BEGIN
                            volume_text := COALESCE(val->>'volume', '0');
                            volume_numeric := sandf.safe_parse_numeric(volume_text);
                        EXCEPTION WHEN OTHERS THEN
                            volume_numeric := 0;
                        END;

                        -- Handle string/numeric premium values properly for display (direct values)
                        DECLARE
                            display_rate TEXT;
                            display_premium TEXT;
                            raw_premium_val TEXT;
                            raw_rate_val TEXT;
                        BEGIN
                            -- Get raw values
                            raw_premium_val := COALESCE(val->>'premium', '-');
                            raw_rate_val := COALESCE(val->>'rate', '-');

                            RAISE NOTICE 'Raw values (direct) - Rate: %, Premium: %', raw_rate_val, raw_premium_val;

                            -- Format for display (handle both string and numeric)
                            IF raw_premium_val = '-' THEN
                                display_premium := '-';
                            ELSE
                                -- If it's already formatted with $, keep it; otherwise add $
                                IF raw_premium_val LIKE '$%' THEN
                                    display_premium := raw_premium_val;
                                ELSE
                                    display_premium := '$' || raw_premium_val;
                                END IF;
                            END IF;

                            IF raw_rate_val = '-' THEN
                                display_rate := '-';
                            ELSE
                                -- If it's already formatted with $, keep it; otherwise add $
                                IF raw_rate_val LIKE '$%' THEN
                                    display_rate := raw_rate_val;
                                ELSE
                                    display_rate := '$' || raw_rate_val;
                                END IF;
                            END IF;

                            RAISE NOTICE 'Display values (direct) - Rate: %, Premium: %', display_rate, display_premium;

                            benefit_values := benefit_values || jsonb_build_object(
                                carrier_name,
                                jsonb_build_object(
                                    'rate', to_jsonb(display_rate),
                                    'volume', to_jsonb(volume_numeric),
                                    'premium', to_jsonb(display_premium)
                                )
                            );
                        END;

                            -- Build section object (store in class sections map)
                            section_obj := jsonb_build_object(
                                'id', section_key,
                                'name', friendly_name,
                                'values', benefit_values,
                                'sort_key', key_name,
                                'base_benefit', key_name,
                                'coverage_type', '',
                                'coverage_order', 0,
                                'class_number', class_number::integer,
                                'employee_class', current_employee_class
                            );

                            -- Store section in class sections map
                            class_sections_map := jsonb_set(
                                class_sections_map,
                                ARRAY[current_employee_class, section_key],
                                section_obj
                            );
                    END IF;
                    END IF; -- End should_include_key check
                END IF;
            END LOOP;
        END LOOP;
    END LOOP;

    -- Step 4.5: Group sections by matching premiums and create final sections array
    DECLARE
        all_section_keys TEXT[];
        section_key_item TEXT;
        class_section JSONB;
        signature_to_classes JSONB := '{}'::jsonb;
        signature_to_section JSONB := '{}'::jsonb;
        processed_signatures TEXT[] := ARRAY[]::TEXT[];
    BEGIN
        -- Get all unique section keys across all classes
        SELECT array_agg(DISTINCT subq.section_key)
        INTO all_section_keys
        FROM (
            SELECT jsonb_object_keys(class_data.class_sections) as section_key
            FROM jsonb_each(class_sections_map) as class_data(class_name, class_sections)
        ) subq;

        RAISE NOTICE 'All section keys: %', all_section_keys;

        -- For each section key, group classes with matching premium values
        FOREACH section_key_item IN ARRAY all_section_keys
        LOOP
            signature_to_classes := '{}'::jsonb;
            signature_to_section := '{}'::jsonb;

            -- Check each class for this section key
            FOR current_employee_class IN
                SELECT jsonb_object_keys(class_sections_map)
            LOOP
                class_section := class_sections_map -> current_employee_class -> section_key_item;

                IF class_section IS NOT NULL THEN
                    -- Create signature from premium values for matching
                    section_signature := '';
                    FOR carrier_name IN
                        SELECT jsonb_object_keys(class_section -> 'values')
                    LOOP
                        section_signature := section_signature || carrier_name || ':' ||
                            COALESCE(class_section -> 'values' -> carrier_name ->> 'rate', '') || '|' ||
                            COALESCE(class_section -> 'values' -> carrier_name ->> 'volume', '') || '|' ||
                            COALESCE(class_section -> 'values' -> carrier_name ->> 'premium', '') || ';';
                    END LOOP;

                    -- Group classes by signature
                    IF signature_to_classes ? section_signature THEN
                        signature_to_classes := jsonb_set(
                            signature_to_classes,
                            ARRAY[section_signature],
                            (signature_to_classes -> section_signature) || jsonb_build_array(current_employee_class)
                        );
                    ELSE
                        signature_to_classes := signature_to_classes || jsonb_build_object(
                            section_signature,
                            jsonb_build_array(current_employee_class)
                        );
                        signature_to_section := signature_to_section || jsonb_build_object(
                            section_signature,
                            class_section
                        );
                    END IF;
                END IF;
            END LOOP;

            -- Create combined sections for each signature
            FOR section_signature IN
                SELECT jsonb_object_keys(signature_to_classes)
            LOOP
                matching_classes := ARRAY(SELECT jsonb_array_elements_text(signature_to_classes -> section_signature));
                section_obj := signature_to_section -> section_signature;

                -- Build className based on matching classes
                IF array_length(matching_classes, 1) = employee_class_count THEN
                    combined_class_name := 'ALL';
                ELSE
                    -- Convert class names to letters and join
                    DECLARE
                        class_letters TEXT[] := ARRAY[]::TEXT[];
                        class_item TEXT;
                    BEGIN
                        FOREACH class_item IN ARRAY matching_classes
                        LOOP
                            class_letters := array_append(class_letters, class_to_letter_map ->> class_item);
                        END LOOP;
                        combined_class_name := array_to_string(class_letters, ',');
                    END;
                END IF;

                -- Create final section with className and proper ID
                section_obj := jsonb_set(section_obj, ARRAY['className'], to_jsonb(combined_class_name));
                section_obj := jsonb_set(section_obj, ARRAY['id'], to_jsonb(section_obj ->> 'id' ||
                    CASE
                        WHEN combined_class_name = 'ALL' THEN ''
                        ELSE replace(combined_class_name, ',', '')
                    END));

                -- Remove metadata fields and add to final sections array
                section_obj := section_obj - 'employee_class' - 'class_number';
                sections_array := sections_array || section_obj;
            END LOOP;
        END LOOP;
    END;
    END; -- End of Step 4 DECLARE block

    -- Step 5: Order sections like original rate-sheet.sql
    DECLARE
        temp_sections_array JSONB := '[]'::jsonb;
        ordered_section RECORD;
        class_order_value INTEGER;
    BEGIN
        -- Order sections by display_order from ui_field table, then by class letter order, then by coverage order
        FOR ordered_section IN
            SELECT
                elem as section_data,
                COALESCE(uf.display_order, 999999) as sort_order,
                elem->>'sort_key' as section_key,
                COALESCE((elem->>'coverage_order')::INT, 999) as coverage_sort,
                elem->>'base_benefit' as base_benefit_name,
                elem->>'coverage_type' as coverage_type_name,
                elem->>'className' as class_name
            FROM jsonb_array_elements(sections_array) elem
            LEFT JOIN sandf.ui_field uf ON uf.name = COALESCE(elem->>'base_benefit',
                CASE
                    WHEN elem->>'sort_key' LIKE '%_%' THEN
                        split_part(elem->>'sort_key', '_', 1)
                    ELSE
                        elem->>'sort_key'
                END)
            ORDER BY
                sort_order ASC,
                base_benefit_name ASC,
                coverage_sort ASC,
                -- Order by class name: ALL first, then A, B, C, etc., then A,B combinations
                CASE
                    WHEN elem->>'className' = 'ALL' THEN 0
                    WHEN elem->>'className' ~ '^[A-Z]$' THEN ascii(elem->>'className') - 64  -- A=1, B=2, etc.
                    ELSE 1000 + length(elem->>'className')  -- A,B combinations come last
                END ASC,
                elem->>'className' ASC,
                section_key ASC
        LOOP
            -- Remove metadata fields like original (keep only id, name, values, className)
            section_obj := ordered_section.section_data - 'sort_key' - 'coverage_order' - 'base_benefit' - 'coverage_type';
            temp_sections_array := temp_sections_array || section_obj;
        END LOOP;
        sections_array := temp_sections_array;
    END;

    -- Step 6: Build calculations from final combined sections
    -- Reset calculation variables
    monthly_premiums_map := '{}'::jsonb;
    annual_premiums_map := '{}'::jsonb;
    calculations_array := '[]'::jsonb;

    RAISE NOTICE 'Starting calculations from final sections array with % sections', jsonb_array_length(sections_array);

    -- Calculate totals for each carrier from the final combined sections
    FOR carrier_name IN
        SELECT DISTINCT jsonb_object_keys(elem -> 'values')
        FROM jsonb_array_elements(sections_array) elem
        WHERE jsonb_typeof(elem -> 'values') = 'object'
    LOOP
        total_monthly_premium := 0;
        total_annual_premium := 0;

        RAISE NOTICE 'Processing carrier: % for final calculations', carrier_name;

        -- Sum premiums from all sections for this carrier
        FOR section_idx IN 0..(jsonb_array_length(sections_array)-1) LOOP
            section_obj := sections_array -> section_idx;

            -- Check if this section has values for this carrier
            IF section_obj -> 'values' ? carrier_name THEN
                DECLARE
                    section_premium_text TEXT;
                    section_premium_value NUMERIC;
                BEGIN
                    section_premium_text := section_obj -> 'values' -> carrier_name ->> 'premium';

                    -- Skip if premium is null, empty, or '-'
                    IF section_premium_text IS NOT NULL AND section_premium_text != '' AND section_premium_text != '-' THEN
                        -- Remove $ sign and parse as numeric
                        section_premium_value := sandf.safe_parse_numeric(replace(section_premium_text, '$', ''));

                        RAISE NOTICE 'Section %, Carrier %, Premium: % -> %',
                            section_obj ->> 'id', carrier_name, section_premium_text, section_premium_value;

                        total_monthly_premium := total_monthly_premium + section_premium_value;
                        total_annual_premium := total_annual_premium + (section_premium_value * 12);
                    END IF;
                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error parsing premium for section % carrier %: %',
                        section_obj ->> 'id', carrier_name, section_premium_text;
                END;
            END IF;
        END LOOP;

        RAISE NOTICE 'Final totals for carrier %: Monthly=%, Annual=%',
            carrier_name, total_monthly_premium, total_annual_premium;

        -- Store calculated totals for this carrier
        monthly_premiums_map := jsonb_set(
            monthly_premiums_map,
            ARRAY[carrier_name],
            to_jsonb(total_monthly_premium)
        );
        annual_premiums_map := jsonb_set(
            annual_premiums_map,
            ARRAY[carrier_name],
            to_jsonb(total_annual_premium)
        );
    END LOOP;

    RAISE NOTICE 'Final monthly premiums map: %', monthly_premiums_map::TEXT;
    RAISE NOTICE 'Final annual premiums map: %', annual_premiums_map::TEXT;

    -- Prepare calculations array with differences and percentages (formatted) in user-preferred order
    IF array_length(ordered_carriers_array, 1) > 0 THEN
        first_annual_premium := COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> ordered_carriers_array[1]), 0);
        RAISE NOTICE 'First carrier: %, First annual premium: %', ordered_carriers_array[1], first_annual_premium;
        FOREACH carrier_name IN ARRAY ordered_carriers_array LOOP
            DECLARE
                carrier_monthly NUMERIC := COALESCE(sandf.safe_parse_numeric(monthly_premiums_map ->> carrier_name), 0);
                carrier_annual NUMERIC := COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> carrier_name), 0);
                dollar_diff TEXT;
                percentage_diff_text TEXT;
            BEGIN
                RAISE NOTICE 'Building calculation for carrier: %, Monthly: %, Annual: %',
                    carrier_name, carrier_monthly, carrier_annual;

                -- Calculate dollar difference
                IF carrier_name = ordered_carriers_array[1] THEN
                    dollar_diff := '-'::TEXT;
                ELSIF carrier_annual = 0 THEN
                    dollar_diff := '-'::TEXT;
                ELSE
                    dollar_diff := '$' || to_char(first_annual_premium - carrier_annual, 'FM999999990.00');
                END IF;

                -- Calculate percentage difference
                IF carrier_name = ordered_carriers_array[1] THEN
                    percentage_diff_text := '-'::TEXT;
                ELSIF carrier_annual = 0 OR first_annual_premium = 0 THEN
                    percentage_diff_text := '-'::TEXT;
                ELSE
                    percentage_diff_text := to_char(((carrier_annual - first_annual_premium) / NULLIF(first_annual_premium, 0)) * 100, 'FM999990.00') || '%';
                END IF;

                RAISE NOTICE 'Calculated differences - Dollar: %, Percentage: %', dollar_diff, percentage_diff_text;

                calc_obj := jsonb_build_object(
                    'carrier', carrier_name,
                    'totalMonthlyPremiums', '$' || to_char(carrier_monthly, 'FM999999990.00'),
                    'annualPremium', '$' || to_char(carrier_annual, 'FM999999990.00'),
                    '$ Difference From #1', dollar_diff,
                    'Percentage Different From #1', percentage_diff_text
                );

                RAISE NOTICE 'Created calculation object: %', calc_obj::TEXT;
                calculations_array := calculations_array || calc_obj;
            END;
        END LOOP;
    END IF;

    RAISE NOTICE 'Final calculations array: %', calculations_array::TEXT;
    RAISE NOTICE 'Total calculations count: %', jsonb_array_length(calculations_array);



    -- Ensure sections_array is not null and count total sections
    IF sections_array IS NULL THEN
        sections_array := '[]'::jsonb;
    END IF;

    total_sections := jsonb_array_length(sections_array);

    RAISE NOTICE 'Total sections found: %', total_sections;

    -- Step 7: Handle pagination with calculations in separate object
    -- Create calculations object that will be added to the last page
    DECLARE
        calculations_object JSONB;
        last_page_index INTEGER;
    BEGIN
        calculations_object := jsonb_build_object(
            'carriers', ordered_carriers_array,
            'sections', '[]'::jsonb,
            'calculations', calculations_array,
            'classLegend', ARRAY['Class Legend: ' || array_to_string(class_legend_array, ', ')]
        );

        -- If total sections <= MAX_SECTIONS_PER_PAGE, put all sections in first object and calculations in second
        IF total_sections <= MAX_SECTIONS_PER_PAGE THEN
            RETURN jsonb_build_array(
                jsonb_build_object(
                    'carriers', ordered_carriers_array,
                    'sections', sections_array
                ),
                calculations_object
            );
        END IF;

        -- Otherwise, paginate by redistributing sections across pages
        current_page_sections := 0;
        current_page_sections_array := '[]'::jsonb;

        -- Only run pagination loop if we have sections
        IF total_sections > 0 THEN
            FOR section_idx IN 0..(total_sections-1) LOOP
                current_page_sections_array := current_page_sections_array || jsonb_build_array(sections_array -> section_idx);
                current_page_sections := current_page_sections + 1;

                -- Check if we've reached the page limit
                IF current_page_sections >= MAX_SECTIONS_PER_PAGE THEN
                    result_pages := result_pages || jsonb_build_array(
                        jsonb_build_object(
                            'carriers', ordered_carriers_array,
                            'sections', current_page_sections_array
                        )
                    );

                    -- Reset for next page
                    current_page_sections_array := '[]'::jsonb;
                    current_page_sections := 0;
                END IF;
            END LOOP;
        END IF;

        -- Add any remaining sections as a page
        IF jsonb_array_length(current_page_sections_array) > 0 THEN
            result_pages := result_pages || jsonb_build_array(
                jsonb_build_object(
                    'carriers', ordered_carriers_array,
                    'sections', current_page_sections_array
                )
            );
        END IF;

        -- Add calculations object to the last page instead of creating a new page
        last_page_index := jsonb_array_length(result_pages) - 1;
        IF last_page_index >= 0 THEN
            -- Add calculations to the last existing page
            result_pages := jsonb_set(
                result_pages,
                ARRAY[last_page_index::text, '0'],
                (result_pages -> last_page_index -> 0) || calculations_object
            );
        ELSE
            -- If no pages exist, create one with just calculations
            result_pages := jsonb_build_array(calculations_object);
        END IF;

        RETURN result_pages;
    END;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in rate sheet v2 function: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
END;
$$;
